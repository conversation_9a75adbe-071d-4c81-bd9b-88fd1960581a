import { useTranslate } from '@/hooks/common-hooks';
import { IModalProps } from '@/interfaces/common';
import {
  Button,
  // Checkbox,
  // Flex,
  Modal,
  Space,
  // Tabs,
  // TabsProps,
  Upload,
  UploadFile,
  UploadProps,
} from 'antd';
import { Dispatch, SetStateAction, useState } from 'react';

const { Dragger } = Upload;

const FileUpload = ({
  directory,
  fileList,
  setFileList,
  uploadProgress,
}: {
  directory: boolean;
  fileList: UploadFile[];
  setFileList: Dispatch<SetStateAction<UploadFile[]>>;
  uploadProgress?: number;
}) => {
  const { t } = useTranslate('fileManager');
  const props: UploadProps = {
    multiple: true,
    onRemove: file => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file: UploadFile) => {
      setFileList(pre => {
        return [...pre, file];
      });

      return false;
    },
    directory,
    fileList,
    progress: {
      strokeWidth: 2,
    },
  };

  // return (
  //   <>
  //     <Progress percent={uploadProgress} showInfo={false} />
  //     <Dragger {...props} className={styles.uploader}>
  //       <p className="ant-upload-drag-icon">
  //         <InboxOutlined />
  //       </p>
  //       <p className="ant-upload-text">{t('uploadTitle')}</p>
  //       <p className="ant-upload-hint">{t('uploadDescription')}</p>
  //       {false && <p className={styles.uploadLimit}>{t('uploadLimit')}</p>}
  //     </Dragger>
  //   </>
  // );

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* 上传区域 - 固定高度 */}
      <div style={{ flexShrink: 0, marginBottom: 16, height: '100%' }}>
        <Dragger {...props} className="uploader">
          <Space align="center">
            <img src="/assets/files/iconupload2.svg" alt="upload" />
            <p
              className="ant-upload-text"
              style={{ color: '#464646', fontSize: '16px', margin: 0 }}
            >
              点击/拖拽文档
            </p>
          </Space>
          <p
            className="ant-upload-hint"
            style={{ color: '#969696', fontSize: '14px' }}
          >
            仅支持.txt/.pdf格式
          </p>
        </Dragger>
      </div>
    </div>
  );
};

interface IFileUploadModalProps
  extends IModalProps<
    { parseOnCreation: boolean; directoryFileList: UploadFile[] } | UploadFile[]
  > {
  uploadFileList?: UploadFile[];
  setUploadFileList?: Dispatch<SetStateAction<UploadFile[]>>;
  uploadProgress?: number;
  setUploadProgress?: Dispatch<SetStateAction<number>>;
}

const FileUploadModal = ({
  visible,
  hideModal,
  loading,
  onOk: onFileUploadOk,
  uploadFileList: fileList,
  setUploadFileList: setFileList,
  uploadProgress,
  setUploadProgress,
}: IFileUploadModalProps) => {
  const { t } = useTranslate('fileManager');
  // const [value, setValue] = useState<string | number>('local');
  // const [parseOnCreation, setParseOnCreation] = useState(false);
  const [currentFileList, setCurrentFileList] = useState<UploadFile[]>([]);
  // const [directoryFileList, setDirectoryFileList] = useState<UploadFile[]>([]);

  const clearFileList = () => {
    if (setFileList) {
      setFileList([]);
      setUploadProgress?.(0);
    } else {
      setCurrentFileList([]);
    }
    // setDirectoryFileList([]);
  };

  const onOk = async () => {
    if (uploadProgress === 100) {
      hideModal?.();
      return;
    }

    //原代码
    // const ret = await onFileUploadOk?.(
    //   fileList
    //     ? { parseOnCreation, directoryFileList }
    //     : [...currentFileList, ...directoryFileList],
    // );
    // return ret;

    //修改后的代码
    const ret = await onFileUploadOk?.(fileList ? fileList : currentFileList);
    return ret;
  };

  const afterClose = () => {
    clearFileList();
  };

  // const items: TabsProps['items'] = [
  //   {
  //     key: '1',
  //     label: t('file'),
  //     children: (
  //       <FileUpload
  //         directory={false}
  //         fileList={fileList ? fileList : currentFileList}
  //         setFileList={setFileList ? setFileList : setCurrentFileList}
  //         uploadProgress={uploadProgress}
  //       ></FileUpload>
  //     ),
  //   },
  //   {
  //     key: '2',
  //     label: t('directory'),
  //     children: (
  //       <FileUpload
  //         directory
  //         fileList={directoryFileList}
  //         setFileList={setDirectoryFileList}
  //         uploadProgress={uploadProgress}
  //       ></FileUpload>
  //     ),
  //   },
  // ];

  // 处理Modal关闭
  const handleCancel = () => {
    clearFileList();
    hideModal?.();
  };

  // 判断上传按钮是否应该禁用
  const isUploadDisabled =
    (fileList ? fileList.length === 0 : currentFileList.length === 0) ||
    loading;

  // 自定义footer
  const customFooter = (
    <div
      style={{
        display: 'flex',
        justifyContent: 'flex-end',
        gap: 12,
        marginTop: 24,
      }}
    >
      <Button
        style={{
          height: 40,
          padding: '8px 24px',
          borderRadius: 8,
          fontSize: 14,
          fontWeight: 500,
          transition: 'all 0.2s ease-in-out',
          boxShadow: 'none',
          border: '1px solid #e8e8e8',
          backgroundColor: '#ffffff',
          color: '#666666',
        }}
        onMouseOver={e => {
          e.currentTarget.style.borderColor = '#d9d9d9';
          e.currentTarget.style.backgroundColor = '#fafafa';
          e.currentTarget.style.color = '#333333';
        }}
        onMouseOut={e => {
          e.currentTarget.style.borderColor = '#e8e8e8';
          e.currentTarget.style.backgroundColor = '#ffffff';
          e.currentTarget.style.color = '#666666';
        }}
        onFocus={e => {
          e.currentTarget.style.borderColor = '#d9d9d9';
          e.currentTarget.style.backgroundColor = '#fafafa';
          e.currentTarget.style.color = '#333333';
        }}
        onBlur={e => {
          e.currentTarget.style.borderColor = '#e8e8e8';
          e.currentTarget.style.backgroundColor = '#ffffff';
          e.currentTarget.style.color = '#666666';
        }}
        onClick={handleCancel}
      >
        取消
      </Button>
      <Button
        type="primary"
        style={{
          height: 40,
          padding: '8px 24px',
          borderRadius: 8,
          fontSize: 14,
          fontWeight: 500,
          transition: 'all 0.2s ease-in-out',
          boxShadow: 'none',
          border: 'none',
          backgroundColor: loading || isUploadDisabled ? '#c2c2c2' : '#1f1f1f',
          color: '#ffffff',
          cursor: loading || isUploadDisabled ? 'not-allowed' : undefined,
        }}
        onMouseOver={e => {
          const btn = e.currentTarget as HTMLButtonElement;
          if (!btn.disabled) {
            btn.style.backgroundColor = '1f1f1f';
            btn.style.color = '#ffffff';
          }
        }}
        onMouseOut={e => {
          const btn = e.currentTarget as HTMLButtonElement;
          if (!btn.disabled) {
            btn.style.backgroundColor = '1f1f1f';
            btn.style.color = '#ffffff';
          }
        }}
        onFocus={e => {
          const btn = e.currentTarget as HTMLButtonElement;
          if (!btn.disabled) {
            btn.style.backgroundColor = '#3441d9';
            btn.style.color = '#ffffff';
          }
        }}
        onBlur={e => {
          const btn = e.currentTarget as HTMLButtonElement;
          if (!btn.disabled) {
            btn.style.backgroundColor = '1f1f1f';
            btn.style.color = '#ffffff';
          }
        }}
        loading={loading}
        disabled={isUploadDisabled}
        onClick={onOk}
      >
        上传
      </Button>
    </div>
  );

  // 由于只有文件上传功能，直接渲染FileUpload组件

  return (
    <>
      {/* <Modal
        title={t('uploadFile')}
        open={visible}
        onOk={onOk}
        onCancel={hideModal}
        confirmLoading={loading}
        afterClose={afterClose}
      >
        <Flex gap={'large'} vertical>
          <Segmented
            options={[
              { label: t('local'), value: 'local' },
              { label: t('s3'), value: 's3' },
            ]}
            block
            value={value}
            onChange={setValue}
          />
          {value === 'local' ? (
            <>
              <Checkbox
                checked={parseOnCreation}
                onChange={(e) => setParseOnCreation(e.target.checked)}
              >
                {t('parseOnCreation')}
              </Checkbox>
              <Tabs defaultActiveKey="1" items={items} />
            </>
          ) : (
            t('comingSoon', { keyPrefix: 'common' })
          )}
        </Flex>
      </Modal> */}

      <Modal
        title={'上传'}
        open={visible}
        onCancel={handleCancel}
        footer={customFooter}
        afterClose={afterClose}
        width={700}
        className="uploadModal"
        styles={{
          body: {
            height: 'auto',
            maxHeight: '60vh',
            overflow: 'auto',
          },
        }}
      >
        <FileUpload
          directory={false}
          fileList={fileList ? fileList : currentFileList}
          setFileList={setFileList ? setFileList : setCurrentFileList}
          uploadProgress={uploadProgress}
        />
      </Modal>
    </>
  );
};

export default FileUploadModal;
