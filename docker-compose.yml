version: '3.8'

services:
  # 前端应用服务
  agentfoundry-ziko:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-https://bs-test-llm.ai4c.cn}
        NEXT_PUBLIC_ZIKO_API_URL: ${NEXT_PUBLIC_ZIKO_API_URL:-http://**********:9380}
        NEXT_PUBLIC_ENV: production
    ports:
      - "${WEB_PORT:-3302}:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-https://bs-test-llm.ai4c.cn}
      - NEXT_PUBLIC_ZIKO_API_URL=${NEXT_PUBLIC_ZIKO_API_URL:-http://**********:9380}
      - NEXT_PUBLIC_ENV=production
    networks:
      - agentfoundry-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.agentfoundry.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.services.agentfoundry.loadbalancer.server.port=3000"

  # Nginx 反向代理服务（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
    depends_on:
      - agentfoundry-ziko
    networks:
      - agentfoundry-network
    restart: unless-stopped
    profiles:
      - with-nginx

networks:
  agentfoundry-network:
    driver: bridge

volumes:
  redis_data:
    driver: local