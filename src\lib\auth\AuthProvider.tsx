'use client';

import { getAuthConfig } from '@/api/auth';
import LoginModal from '@/components/auth/LoginModal';
import { setAuthBlocked } from '@/lib/http/errorInterceptor';
import { ErrorType, useGlobalError } from '@/providers/GlobalErrorProvider';
import { AuthContextType, UserProfile } from '@/types/auth';
import {
  useBoolean,
  useLocalStorageState,
  useMount,
  useRequest,
  useUpdateEffect,
} from 'ahooks';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from 'react';
import {
  getToken,
  getUserProfile,
  initializeKeycloak,
  keycloakInstance,
  triggerLogin,
  triggerLogout,
} from './keycloak';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  // 使用ahooks优化状态管理
  const [
    authenticated,
    { setTrue: setAuthenticated, setFalse: setUnauthenticated },
  ] = useBoolean(false);
  const [userProfile, setUserProfile] =
    useLocalStorageState<UserProfile | null>('userProfile', {
      defaultValue: null,
    });
  const [token, setToken] = useLocalStorageState<string | null>('authToken', {
    defaultValue: null,
  });
  const [isInitialized, { setTrue: setInitialized }] = useBoolean(false);

  // 认证错误处理状态
  const [showLoginModal, { setTrue: showLogin, setFalse: hideLogin }] =
    useBoolean(false);
  const [authErrorMessage, setAuthErrorMessage] = React.useState<string>('');

  // 组件挂载状态检查
  const isMountedRef = useRef(false);

  // 获取全局错误处理器
  const { registerErrorObserver, unregisterErrorObserver } = useGlobalError();

  // 使用 ahooks 的 useRequest 来处理认证配置获取
  const { run: initAuth, loading: initLoading } = useRequest(
    async () => {
      const config = await getAuthConfig();
      console.log('🔐 初始化Keycloak', config);

      await initializeKeycloak(config, () => {
        // 认证成功回调
        const profile = getUserProfile();
        const currentToken = getToken();

        if (profile && currentToken) {
          console.log('🔐 Keycloak认证成功，更新用户状态');
          setUserProfile(profile);
          setToken(currentToken);
          setAuthenticated();
        }
      });

      console.log('🔐 Keycloak初始化完成');
      setInitialized();
      return true;
    },
    {
      manual: true,
      cacheKey: 'auth-init',
      cacheTime: 5 * 60 * 1000, // 缓存5分钟
      retryCount: 0, // 不重试
      onError: error => {
        console.error('Failed to initialize auth:', error);
        setUnauthenticated();
        setInitialized();
        setAuthErrorMessage(error.message || '认证失败，请重新登录');
        showLogin();
      },
    }
  );

  useMount(() => {
    isMountedRef.current = true;
    initAuth();
  });

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // 监听页面焦点变化，重新检查认证状态
  // useEffect(() => {
  //   const handleFocus = async () => {
  //     if (keycloakInstance && isInitialized) {
  //       try {
  //         // 检查token是否仍然有效
  //         const isValid = !keycloakInstance.isTokenExpired();
  //         if (isValid && !authenticated) {
  //           console.log("🔐 检测到有效token，更新认证状态");
  //           const profile = getUserProfile();
  //           const currentToken = getToken();

  //           if (profile && currentToken) {
  //             setUserProfile(profile);
  //             setToken(currentToken);
  //             setAuthenticated(true);
  //           }
  //         } else if (!isValid && authenticated) {
  //           console.log("🔐 Token已过期，清除认证状态");
  //           setAuthenticated(false);
  //           setUserProfile(null);
  //           setToken(null);
  //         }
  //       } catch (error) {
  //         console.warn("检查认证状态时出错:", error);
  //       }
  //     }
  //   };

  //   window.addEventListener("focus", handleFocus);

  // 页面加载时也检查一次
  //   handleFocus();

  //   return () => {
  //     window.removeEventListener("focus", handleFocus);
  //   };
  // }, [authenticated, isInitialized]);

  const login = () => {
    console.log('🔐 触发Keycloak登录');
    triggerLogin();
  };

  const logout = useCallback(
    (redirectUri?: string) => {
      triggerLogout(redirectUri);
      setUnauthenticated();
      setUserProfile(null);
      setToken(null);
      hideLogin();
      setAuthErrorMessage('');
    },
    [setUnauthenticated, setUserProfile, setToken, hideLogin]
  );

  const handleAuthErrorRef = useRef<(error: any) => void>(() => {});

  handleAuthErrorRef.current = useCallback(
    (error: any) => {
      if (!isMountedRef.current) {
        console.warn('🔐 组件未挂载，忽略认证错误处理');
        return;
      }

      console.log('🔐 AuthProvider处理认证错误:', error);

      // 设置认证状态为失败
      setUnauthenticated();
      setUserProfile(null);
      setToken(null);

      // 设置错误消息
      setAuthErrorMessage(error.message || '认证失败，请重新登录');

      // 显示登录弹窗
      showLogin();

      // 检查是否需要阻塞请求
      if (error.details?.shouldBlockRequests) {
        console.log('🔐 阻塞请求，等待用户重新认证');
        setAuthBlocked(true, createAuthPromise());
      }
    },
    [setUnauthenticated, setUserProfile, setToken, showLogin]
  );

  const handleAuthError = useCallback((error: any) => {
    handleAuthErrorRef.current?.(error);
  }, []);

  // 关闭登录弹窗
  const dismissLoginModal = useCallback(() => {
    hideLogin();
    setAuthErrorMessage('');
    setAuthBlocked(false);
  }, [hideLogin]);

  // 创建认证Promise，用于请求队列等待
  const createAuthPromise = (): Promise<boolean> => {
    return new Promise(resolve => {
      const checkAuth = () => {
        if (authenticated) {
          resolve(true);
          return;
        }
        setTimeout(checkAuth, 1000);
      };
      checkAuth();

      // 5分钟后超时
      setTimeout(() => resolve(false), 5 * 60 * 1000);
    });
  };

  const contextValue: AuthContextType = useMemo(
    () => ({
      isAuthenticated: authenticated,
      setAuthenticated,
      userProfile,
      user: userProfile, // 添加user属性作为userProfile的别名
      keycloakInstance,
      login,
      logout,
      token,
      isInitialized,
    }),
    [authenticated, userProfile, token, isInitialized, login, logout]
  );

  // 注册认证错误观察者 - 只在初始化完成后注册
  useEffect(() => {
    if (!isInitialized || !isMountedRef.current) {
      return;
    }

    const observerId = 'auth-provider';

    // 注册观察者，监听认证错误
    registerErrorObserver(observerId, error => {
      if (error.type === ErrorType.AUTH) {
        handleAuthError(error);
      }
    });

    // 清理时取消注册
    return () => {
      unregisterErrorObserver(observerId);
    };
  }, [
    isInitialized,
    registerErrorObserver,
    unregisterErrorObserver,
    handleAuthError,
  ]);

  // 监听认证成功，自动关闭登录弹窗 - 使用 useUpdateEffect 避免初始渲染时触发
  useUpdateEffect(() => {
    if (authenticated && showLoginModal) {
      console.log('🔐 认证成功，关闭登录弹窗');
      dismissLoginModal();
    }
  }, [authenticated, showLoginModal]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}

      {/* 认证错误处理 - 登录弹窗 */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={dismissLoginModal}
        reason={authErrorMessage || '需要重新登录以继续操作'}
      />
    </AuthContext.Provider>
  );
}

export function useAuthContext(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}

// 提供 useAuth 别名以保持一致性
export const useAuth = useAuthContext;
