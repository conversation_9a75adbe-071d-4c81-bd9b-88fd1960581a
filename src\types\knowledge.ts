/**
 * 知识库核心类型定义
 * 基于源项目 agentfoundry-ui 的知识库数据结构
 * 适配当前项目的 TypeScript 类型系统
 */

// 运行状态枚举
export enum RunningStatus {
  UNSTART = '0',
  RUNNING = '1',
  CANCEL = '2',
  DONE = '3',
  FAIL = '4',
}

// 知识库权限类型
export type KnowledgePermission = 'public' | 'partial' | 'me';

// 知识库状态类型
export type KnowledgeStatus = 'active' | 'inactive' | 'updating';

// 文档类型
export type DocumentType =
  | 'pdf'
  | 'doc'
  | 'docx'
  | 'txt'
  | 'md'
  | 'html'
  | 'json';

// 解析器配置类型
export interface ParserConfig {
  from_page?: number;
  to_page?: number;
  auto_keywords?: number;
  auto_questions?: number;
  chunk_token_num?: number;
  delimiter?: string;
  html4excel?: boolean;
  layout_recognize?: boolean;
  raptor?: {
    use_raptor?: boolean;
    raptor_config?: Record<string, any>;
  };
  tag_kb_ids?: string[];
  topn_tags?: number;
  graphrag?: {
    use_graphrag?: boolean;
  };
}

// 知识库基础类型
export interface IKnowledge {
  id: string;
  name: string;
  description: string;

  // API返回的字段
  permission: string;
  chunk_count: number;
  document_count: number;
  create_time: string;
  update_time: string;
  creator_name?: string;
  creator_role?: string;
  tags?: string[];
  size?: number;
  chunk_method?: string;
  embedding_model?: string;
  language?: string;

  // 向后兼容的字段映射
  chunk_num: number;
  doc_num: number;
  token_num?: number;
  status: string;
  parser_config?: ParserConfig;
  parser_id?: string;
  embd_id?: string;
  similarity_threshold: number;
  vector_similarity_weight: number;
  operator_permission?: number;
}

// 文档解析配置类型
export interface IKnowledgeFileParserConfig {
  chunk_token_num?: number;
  delimiter?: string;
  html4excel?: boolean;
  layout_recognize?: boolean;
  from_page?: number;
  to_page?: number;
  auto_keywords?: number;
  auto_questions?: number;
}

// 文档信息类型
export interface IDocument {
  id: string;
  kb_id: string;
  name: string;
  location: string;
  size: number;
  type: DocumentType;
  source_type: string;
  thumbnail?: string; // base64 格式的缩略图

  // 处理状态
  progress: number; // 解析进度 0-100
  progress_msg: string; // 解析日志信息
  run: RunningStatus; // 解析状态
  status: string; // enabled/disabled

  // 统计信息
  chunk_num: number;
  token_num: number;

  // 解析配置
  parser_config: IKnowledgeFileParserConfig;
  parser_id: string;

  // 处理时间信息
  process_begin_at?: string;
  process_duation: number; // 处理持续时间（秒）

  // 时间戳
  create_date: string;
  create_time: number;
  update_date: string;
  update_time: number;

  // 用户信息
  created_by: string;
}

// 知识块类型
export interface IChunk {
  chunk_id: string;
  doc_id: string;
  doc_name: string;

  // 内容信息
  content_with_weight: string;
  available_int: number; // 0: 禁用, 1: 启用

  // 图片和位置信息
  image_id?: string;
  positions?: number[][]; // 在文档中的位置坐标

  // 关键词信息
  important_kwd?: string[]; // 重要关键词
  question_kwd?: string[]; // 问题关键词
  tag_kwd?: string[]; // 标签关键词

  // 特征向量
  tag_feas?: Record<string, number>;
}

// 检索结果类型
export interface RetrievalResult {
  chunk_id: string;
  doc_id: string;
  doc_name: string;
  content: string;
  similarity: number; // 相似度分数 0-1
  keywords?: string[];
  highlights?: Array<{
    start: number;
    end: number;
    text: string;
  }>;
}

// 检索设置类型
export interface RetrievalSettings {
  similarity_threshold: number; // 相似度阈值
  vector_similarity_weight: number; // 向量相似度权重
  top_k: number; // 返回结果数量
  rerank?: boolean; // 是否重排序
}

// 检索请求参数
export interface RetrievalParams {
  query: string;
  kb_id: string;
  similarity_threshold: number;
  vector_similarity_weight: number;
  top_k: number;
  doc_ids?: string[];
  chunk_ids?: string[];
}

// 上传文件参数类型
export interface UploadFileParams {
  files: File[];
  parser_config?: Partial<ParserConfig>;
  run_type?: 'immediate' | 'manual'; // 立即解析 或 手动触发
  onProgress?: (progress: number) => void; // 进度回调
}

// 知识库创建参数类型
export interface KnowledgeCreateParams {
  name: string;
  description?: string;
  avatar?: string;
  permission?: KnowledgePermission;
  parser_config?: Partial<ParserConfig>;
  similarity_threshold?: number;
  vector_similarity_weight?: number;
  embd_id?: string;
}

// 知识库更新参数类型
export interface KnowledgeUpdateParams {
  parser_id: string;
  name?: string;
  description?: string;
  avatar?: string;
  permission?: KnowledgePermission;
  parser_config?: Partial<ParserConfig>;
  similarity_threshold?: number;
  vector_similarity_weight?: number;
  tags?: string[];
}

// 分页信息类型
export interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
  hasMore: boolean;
}

// 批量操作参数类型
export interface BatchOperationParams {
  action: 'enable' | 'disable' | 'delete';
  ids: string[];
  chunk_ids: string[]; // 知识块ID列表（向后兼容）
  reason?: string; // 操作原因
}

// 文件处理任务类型
export interface ProcessingTask {
  id: string;
  doc_id: string;
  doc_name: string;
  kb_id: string;
  kb_name: string;
  status: RunningStatus;
  progress: number;
  message: string;
  created_at: string;
  updated_at: string;
}

// 操作日志类型
export interface OperationLog {
  id: string;
  kb_id: string;
  user_id: string;
  user_name: string;
  action: string; // 操作类型
  target: string; // 操作目标
  details: Record<string, any>; // 操作详情
  timestamp: number;
}

// 知识库统计信息类型
export interface KnowledgeStats {
  total_documents: number;
  total_chunks: number;
  total_tokens: number;
  total_size: number; // 文件总大小（字节）
  document_types: Record<DocumentType, number>; // 各文档类型数量
  processing_status: Record<RunningStatus, number>; // 各处理状态数量
  recent_activity: OperationLog[]; // 最近操作记录
}

// 导出所有类型的联合类型，便于使用
export type KnowledgeRelatedTypes =
  | IKnowledge
  | IDocument
  | IChunk
  | RetrievalResult
  | ProcessingTask
  | OperationLog
  | KnowledgeStats;
