import type { NextConfig } from 'next';

let authConfig;
const nextConfig: NextConfig = async () => {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL;
  if (!apiUrl) {
    throw new Error('NEXT_PUBLIC_API_URL is not set');
  }

  return {
    // Docker 容器化输出模式
    output: 'standalone',

    // 关闭TypeScript类型检查以解决构建问题
    typescript: {
      ignoreBuildErrors: true,
    },
    eslint: {
      ignoreDuringBuilds: true,
    },
    // 启用实验性功能以提升性能
    experimental: {
      optimizeCss: true, // 暂时关闭，需要critters依赖
      optimizePackageImports: [
        'ahooks',
        'antd',
        'lucide-react',
        'react-pdf-highlighter',
      ],
    },
    turbopack: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
      resolveAlias: {
        canvas: './turbopack-canvas-stub.js',
      },
    },
    webpack(config: any) {
      config.module.rules.push({
        test: /\.svg$/,
        use: ['@svgr/webpack'],
      });

      // 解决 pdfjs-dist 的 Node.js 模块依赖问题
      config.resolve.alias = {
        ...config.resolve.alias,
        canvas: false,
      };

      return config;
    },

    async rewrites() {
      return [
        {
          source: '/api/:path*',
          destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
        },
        {
          source: '/ws/:path*',
          destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
        },
        {
          source: '/v1/:path*',
          destination: `${process.env.NEXT_PUBLIC_ZIKO_API_URL}/v1/:path*`,
        },
      ];
    },
  };
};

export default nextConfig;
